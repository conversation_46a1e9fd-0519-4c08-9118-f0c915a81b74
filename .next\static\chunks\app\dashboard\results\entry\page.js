/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/results/entry/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CIELTS-Certification-System%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cresults%5C%5Centry%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CIELTS-Certification-System%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cresults%5C%5Centry%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/results/entry/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDV2luZG93cyUyMDExJTVDJTVDRGVza3RvcCU1QyU1Q2NvZGVzJTVDJTVDSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNyZXN1bHRzJTVDJTVDZW50cnklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhNQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEZXNrdG9wXFxcXGNvZGVzXFxcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHJlc3VsdHNcXFxcZW50cnlcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CIELTS-Certification-System%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cresults%5C%5Centry%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js":
/*!****************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/Icon.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Icon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && {\n            \"aria-hidden\": \"true\"\n        },\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBd0JBLENBQU0sU0FBTyx3RUFDWCxRQVdFLENBRUE7UUFaQSxFQUNFLEtBQVEsbUJBQ1IsSUFBTyxPQUNQLFdBQWMsTUFDZCxxQkFDQSxTQUFZLE9BQ1osVUFDQSxVQUNBLEdBQUc7eUJBSUwscURBQ0UsT0FDQTtRQUNFO1FBQ0EsR0FBRztRQUNILEtBQU87UUFDUCxNQUFRO1FBQ1IsTUFBUTtRQUNSLFlBQWEscUJBQXVCLFFBQU8sRUFBVyxZQUFJLElBQU0sVUFBTyxDQUFJLElBQUk7UUFDL0UsVUFBVyxtRUFBYSxXQUFVLFNBQVM7UUFDM0MsQ0FBSSxHQUFDLENBQVksWUFBQyxpRUFBVyxDQUFDLENBQUksS0FBSztZQUFFLGVBQWUsTUFBTztRQUFBO1FBQy9ELEdBQUc7SUFDTCxHQUNBO1dBQ0ssQ0FBUyxZQUFJO2dCQUFDLENBQUMsQ0FBSyxLQUFLLEtBQU07aUNBQUEscURBQWMsR0FBSyxPQUFLLENBQUM7O1dBQ3ZELEtBQU0sU0FBUSxRQUFRLENBQUksY0FBVztZQUFDLFFBQVE7U0FBQTtLQUNwRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxzcmNcXEljb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRWxlbWVudCwgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBkZWZhdWx0QXR0cmlidXRlcyBmcm9tICcuL2RlZmF1bHRBdHRyaWJ1dGVzJztcbmltcG9ydCB7IEljb25Ob2RlLCBMdWNpZGVQcm9wcyB9IGZyb20gJy4vdHlwZXMnO1xuaW1wb3J0IHsgbWVyZ2VDbGFzc2VzLCBoYXNBMTF5UHJvcCB9IGZyb20gJ0BsdWNpZGUvc2hhcmVkJztcblxuaW50ZXJmYWNlIEljb25Db21wb25lbnRQcm9wcyBleHRlbmRzIEx1Y2lkZVByb3BzIHtcbiAgaWNvbk5vZGU6IEljb25Ob2RlO1xufVxuXG4vKipcbiAqIEx1Y2lkZSBpY29uIGNvbXBvbmVudFxuICpcbiAqIEBjb21wb25lbnQgSWNvblxuICogQHBhcmFtIHtvYmplY3R9IHByb3BzXG4gKiBAcGFyYW0ge3N0cmluZ30gcHJvcHMuY29sb3IgLSBUaGUgY29sb3Igb2YgdGhlIGljb25cbiAqIEBwYXJhbSB7bnVtYmVyfSBwcm9wcy5zaXplIC0gVGhlIHNpemUgb2YgdGhlIGljb25cbiAqIEBwYXJhbSB7bnVtYmVyfSBwcm9wcy5zdHJva2VXaWR0aCAtIFRoZSBzdHJva2Ugd2lkdGggb2YgdGhlIGljb25cbiAqIEBwYXJhbSB7Ym9vbGVhbn0gcHJvcHMuYWJzb2x1dGVTdHJva2VXaWR0aCAtIFdoZXRoZXIgdG8gdXNlIGFic29sdXRlIHN0cm9rZSB3aWR0aFxuICogQHBhcmFtIHtzdHJpbmd9IHByb3BzLmNsYXNzTmFtZSAtIFRoZSBjbGFzcyBuYW1lIG9mIHRoZSBpY29uXG4gKiBAcGFyYW0ge0ljb25Ob2RlfSBwcm9wcy5jaGlsZHJlbiAtIFRoZSBjaGlsZHJlbiBvZiB0aGUgaWNvblxuICogQHBhcmFtIHtJY29uTm9kZX0gcHJvcHMuaWNvbk5vZGUgLSBUaGUgaWNvbiBub2RlIG9mIHRoZSBpY29uXG4gKlxuICogQHJldHVybnMge0ZvcndhcmRSZWZFeG90aWNDb21wb25lbnR9IEx1Y2lkZUljb25cbiAqL1xuY29uc3QgSWNvbiA9IGZvcndhcmRSZWY8U1ZHU1ZHRWxlbWVudCwgSWNvbkNvbXBvbmVudFByb3BzPihcbiAgKFxuICAgIHtcbiAgICAgIGNvbG9yID0gJ2N1cnJlbnRDb2xvcicsXG4gICAgICBzaXplID0gMjQsXG4gICAgICBzdHJva2VXaWR0aCA9IDIsXG4gICAgICBhYnNvbHV0ZVN0cm9rZVdpZHRoLFxuICAgICAgY2xhc3NOYW1lID0gJycsXG4gICAgICBjaGlsZHJlbixcbiAgICAgIGljb25Ob2RlLFxuICAgICAgLi4ucmVzdFxuICAgIH0sXG4gICAgcmVmLFxuICApID0+XG4gICAgY3JlYXRlRWxlbWVudChcbiAgICAgICdzdmcnLFxuICAgICAge1xuICAgICAgICByZWYsXG4gICAgICAgIC4uLmRlZmF1bHRBdHRyaWJ1dGVzLFxuICAgICAgICB3aWR0aDogc2l6ZSxcbiAgICAgICAgaGVpZ2h0OiBzaXplLFxuICAgICAgICBzdHJva2U6IGNvbG9yLFxuICAgICAgICBzdHJva2VXaWR0aDogYWJzb2x1dGVTdHJva2VXaWR0aCA/IChOdW1iZXIoc3Ryb2tlV2lkdGgpICogMjQpIC8gTnVtYmVyKHNpemUpIDogc3Ryb2tlV2lkdGgsXG4gICAgICAgIGNsYXNzTmFtZTogbWVyZ2VDbGFzc2VzKCdsdWNpZGUnLCBjbGFzc05hbWUpLFxuICAgICAgICAuLi4oIWNoaWxkcmVuICYmICFoYXNBMTF5UHJvcChyZXN0KSAmJiB7ICdhcmlhLWhpZGRlbic6ICd0cnVlJyB9KSxcbiAgICAgICAgLi4ucmVzdCxcbiAgICAgIH0sXG4gICAgICBbXG4gICAgICAgIC4uLmljb25Ob2RlLm1hcCgoW3RhZywgYXR0cnNdKSA9PiBjcmVhdGVFbGVtZW50KHRhZywgYXR0cnMpKSxcbiAgICAgICAgLi4uKEFycmF5LmlzQXJyYXkoY2hpbGRyZW4pID8gY2hpbGRyZW4gOiBbY2hpbGRyZW5dKSxcbiAgICAgIF0sXG4gICAgKSxcbik7XG5cbmV4cG9ydCBkZWZhdWx0IEljb247XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))), \"lucide-\".concat(iconName), className),\n            ...props\n        });\n    });\n    Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vY3JlYXRlTHVjaWRlSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBV00sdUJBQW1CLEdBQUMsVUFBa0IsUUFBdUI7SUFDakUsTUFBTSxDQUFZLDJFQUF1QyxRQUEwQjtZQUF6QixFQUFFLENBQVcsV0FBRyxRQUFTOzZCQUNqRixvREFBYSxDQUFDLGdEQUFNO1lBQ2xCO1lBQ0E7WUFDQSxTQUFXLHFFQUNULENBQVUsU0FBbUMsT0FBbkMsa0VBQVksa0VBQWEsRUFBUSxRQUFDLENBQUMsR0FDN0MsUUFBVSxFQUFRLE9BQVIsUUFBUSxHQUNsQjtZQUVGLEdBQUc7UUFBQSxDQUNKOztJQUdPLHdCQUFjLGtFQUFZLENBQUMsUUFBUTtJQUV0QztBQUNUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXHNyY1xcY3JlYXRlTHVjaWRlSWNvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVFbGVtZW50LCBmb3J3YXJkUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbWVyZ2VDbGFzc2VzLCB0b0tlYmFiQ2FzZSwgdG9QYXNjYWxDYXNlIH0gZnJvbSAnQGx1Y2lkZS9zaGFyZWQnO1xuaW1wb3J0IHsgSWNvbk5vZGUsIEx1Y2lkZVByb3BzIH0gZnJvbSAnLi90eXBlcyc7XG5pbXBvcnQgSWNvbiBmcm9tICcuL0ljb24nO1xuXG4vKipcbiAqIENyZWF0ZSBhIEx1Y2lkZSBpY29uIGNvbXBvbmVudFxuICogQHBhcmFtIHtzdHJpbmd9IGljb25OYW1lXG4gKiBAcGFyYW0ge2FycmF5fSBpY29uTm9kZVxuICogQHJldHVybnMge0ZvcndhcmRSZWZFeG90aWNDb21wb25lbnR9IEx1Y2lkZUljb25cbiAqL1xuY29uc3QgY3JlYXRlTHVjaWRlSWNvbiA9IChpY29uTmFtZTogc3RyaW5nLCBpY29uTm9kZTogSWNvbk5vZGUpID0+IHtcbiAgY29uc3QgQ29tcG9uZW50ID0gZm9yd2FyZFJlZjxTVkdTVkdFbGVtZW50LCBMdWNpZGVQcm9wcz4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+XG4gICAgY3JlYXRlRWxlbWVudChJY29uLCB7XG4gICAgICByZWYsXG4gICAgICBpY29uTm9kZSxcbiAgICAgIGNsYXNzTmFtZTogbWVyZ2VDbGFzc2VzKFxuICAgICAgICBgbHVjaWRlLSR7dG9LZWJhYkNhc2UodG9QYXNjYWxDYXNlKGljb25OYW1lKSl9YCxcbiAgICAgICAgYGx1Y2lkZS0ke2ljb25OYW1lfWAsXG4gICAgICAgIGNsYXNzTmFtZSxcbiAgICAgICksXG4gICAgICAuLi5wcm9wcyxcbiAgICB9KSxcbiAgKTtcblxuICBDb21wb25lbnQuZGlzcGxheU5hbWUgPSB0b1Bhc2NhbENhc2UoaWNvbk5hbWUpO1xuXG4gIHJldHVybiBDb21wb25lbnQ7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBjcmVhdGVMdWNpZGVJY29uO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0lBQUEsQ0FBZTtJQUNiLEtBQU87SUFDUCxLQUFPO0lBQ1AsTUFBUTtJQUNSLE9BQVM7SUFDVCxJQUFNO0lBQ04sTUFBUTtJQUNSLFdBQWE7SUFDYixhQUFlO0lBQ2YsY0FBZ0I7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcc3JjXFxkZWZhdWx0QXR0cmlidXRlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XG4gIHhtbG5zOiAnaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnLFxuICB3aWR0aDogMjQsXG4gIGhlaWdodDogMjQsXG4gIHZpZXdCb3g6ICcwIDAgMjQgMjQnLFxuICBmaWxsOiAnbm9uZScsXG4gIHN0cm9rZTogJ2N1cnJlbnRDb2xvcicsXG4gIHN0cm9rZVdpZHRoOiAyLFxuICBzdHJva2VMaW5lY2FwOiAncm91bmQnLFxuICBzdHJva2VMaW5lam9pbjogJ3JvdW5kJyxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Check)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 6 9 17l-5-5\",\n            key: \"1gmf2c\"\n        }\n    ]\n];\nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"check\", __iconNode);\n //# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsaUJBQXVCO0lBQUM7UUFBQyxNQUFRO1FBQUEsQ0FBRTtZQUFBLEVBQUcsa0JBQW1CO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhaEYsWUFBUSxrRUFBaUIsVUFBUyxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXHNyY1xcaWNvbnNcXGNoZWNrLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdNMjAgNiA5IDE3bC01LTUnLCBrZXk6ICcxZ21mMmMnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZWNrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NakFnTmlBNUlERTNiQzAxTFRVaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZWNrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hlY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdjaGVjaycsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGVjaztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n];\nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-alert\", __iconNode);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLWFsZXJ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFNLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekQ7UUFBQztRQUFRLENBQUU7WUFBQSxJQUFJLENBQU07WUFBQSxJQUFJLENBQU07WUFBQSxHQUFJLElBQUs7WUFBQSxHQUFJLEtBQU07WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ2pFO1FBQUM7UUFBUSxDQUFFO1lBQUEsSUFBSSxDQUFNO1lBQUEsSUFBSSxDQUFTO1lBQUEsR0FBSSxLQUFNO1lBQUEsR0FBSSxLQUFNO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUN2RTtBQWFNLGtCQUFjLGtFQUFpQixpQkFBZ0IsQ0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxzcmNcXGljb25zXFxjaXJjbGUtYWxlcnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbJ2NpcmNsZScsIHsgY3g6ICcxMicsIGN5OiAnMTInLCByOiAnMTAnLCBrZXk6ICcxbWdsYXknIH1dLFxuICBbJ2xpbmUnLCB7IHgxOiAnMTInLCB4MjogJzEyJywgeTE6ICc4JywgeTI6ICcxMicsIGtleTogJzFwa2V1aCcgfV0sXG4gIFsnbGluZScsIHsgeDE6ICcxMicsIHgyOiAnMTIuMDEnLCB5MTogJzE2JywgeTI6ICcxNicsIGtleTogJzRkZnE5MCcgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2lyY2xlQWxlcnRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOFkybHlZMnhsSUdONFBTSXhNaUlnWTNrOUlqRXlJaUJ5UFNJeE1DSWdMejRLSUNBOGJHbHVaU0I0TVQwaU1USWlJSGd5UFNJeE1pSWdlVEU5SWpnaUlIa3lQU0l4TWlJZ0x6NEtJQ0E4YkdsdVpTQjRNVDBpTVRJaUlIZ3lQU0l4TWk0d01TSWdlVEU5SWpFMklpQjVNajBpTVRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NpcmNsZS1hbGVydFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENpcmNsZUFsZXJ0ID0gY3JlYXRlTHVjaWRlSWNvbignY2lyY2xlLWFsZXJ0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENpcmNsZUFsZXJ0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Eye)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0\",\n            key: \"1nclc0\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n];\nconst Eye = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"eye\", __iconNode);\n //# sourceMappingURL=eye.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasA11yProp: () => (/* binding */ hasA11yProp),\n/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),\n/* harmony export */   toCamelCase: () => (/* binding */ toCamelCase),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   toPascalCase: () => (/* binding */ toPascalCase)\n/* harmony export */ });\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string)=>string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = (string)=>{\n    const camelCase = toCamelCase(string);\n    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\nconst hasA11yProp = (props)=>{\n    for(const prop in props){\n        if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n            return true;\n        }\n    }\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxjb2Rlc1xcSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW1cXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/results/entry/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResultsEntryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst BAND_SCORES = [\n    1.0,\n    1.5,\n    2.0,\n    2.5,\n    3.0,\n    3.5,\n    4.0,\n    4.5,\n    5.0,\n    5.5,\n    6.0,\n    6.5,\n    7.0,\n    7.5,\n    8.0,\n    8.5,\n    9.0\n];\nfunction ResultsEntryPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [candidates, setCandidates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bandScores, setBandScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResultsEntryPage.useEffect\": ()=>{\n            if (status === 'loading') return;\n            if (!session) {\n                router.push('/auth/signin');\n                return;\n            }\n            fetchCandidates();\n        }\n    }[\"ResultsEntryPage.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    const fetchCandidates = async ()=>{\n        try {\n            const response = await fetch('/api/checker/candidates?includeResults=true');\n            if (response.ok) {\n                var _data_candidates;\n                const data = await response.json();\n                setCandidates(data.candidates || []);\n                // Initialize band scores from existing results\n                const initialScores = {};\n                (_data_candidates = data.candidates) === null || _data_candidates === void 0 ? void 0 : _data_candidates.forEach((candidate)=>{\n                    if (candidate.result) {\n                        initialScores[candidate.id] = {\n                            listening: candidate.result.listeningBandScore ? parseFloat(candidate.result.listeningBandScore.toString()) : null,\n                            reading: candidate.result.readingBandScore ? parseFloat(candidate.result.readingBandScore.toString()) : null,\n                            writingTask1: candidate.result.writingTask1Score ? parseFloat(candidate.result.writingTask1Score.toString()) : null,\n                            writingTask2: candidate.result.writingTask2Score ? parseFloat(candidate.result.writingTask2Score.toString()) : null,\n                            speaking: candidate.result.speakingBandScore ? parseFloat(candidate.result.speakingBandScore.toString()) : null\n                        };\n                    }\n                });\n                setBandScores(initialScores);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to fetch candidates');\n            }\n        } catch (error) {\n            console.error('Error fetching candidates:', error);\n            setError('An error occurred while fetching candidates');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateBandScore = (candidateId, skill, score)=>{\n        setBandScores((prev)=>({\n                ...prev,\n                [candidateId]: {\n                    ...prev[candidateId],\n                    [skill]: score\n                }\n            }));\n    };\n    const calculateOverallBandScore = (scores)=>{\n        const { listening, reading, writingTask1, writingTask2, speaking } = scores;\n        // Calculate writing band score from tasks\n        let writingBand = null;\n        if (writingTask1 !== null && writingTask2 !== null) {\n            writingBand = (writingTask1 + writingTask2) / 2;\n        }\n        const validScores = [\n            listening,\n            reading,\n            writingBand,\n            speaking\n        ].filter((score)=>score !== null);\n        if (validScores.length === 4) {\n            const average = validScores.reduce((sum, score)=>sum + score, 0) / 4;\n            return Math.round(average * 2) / 2; // Round to nearest 0.5\n        }\n        return null;\n    };\n    const saveBandScore = async (candidateId, skill)=>{\n        const scores = bandScores[candidateId];\n        if (!scores) return;\n        setIsSaving(true);\n        try {\n            var _candidate_result;\n            const candidate = candidates.find((c)=>c.id === candidateId);\n            if (!candidate) return;\n            const overallBandScore = calculateOverallBandScore(scores);\n            // Prepare the data for API\n            const resultData = {\n                candidateId,\n                listeningBandScore: scores.listening,\n                readingBandScore: scores.reading,\n                writingTask1Score: scores.writingTask1,\n                writingTask2Score: scores.writingTask2,\n                writingBandScore: scores.writingTask1 && scores.writingTask2 ? (scores.writingTask1 + scores.writingTask2) / 2 : null,\n                speakingBandScore: scores.speaking,\n                overallBandScore,\n                status: 'completed'\n            };\n            const url = candidate.hasResult ? \"/api/checker/results/\".concat((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) : '/api/checker/results';\n            const method = candidate.hasResult ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(resultData)\n            });\n            if (response.ok) {\n                setSuccessMessage(\"\".concat(skill, \" score saved for \").concat(candidate.candidateNumber));\n                setTimeout(()=>setSuccessMessage(''), 3000);\n                await fetchCandidates(); // Refresh data\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to save score');\n            }\n        } catch (error) {\n            console.error('Error saving score:', error);\n            setError('An error occurred while saving the score');\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400';\n        if (score >= 8.5) return 'bg-green-100 text-green-800';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800';\n        return 'bg-red-100 text-red-800';\n    };\n    if (status === 'loading' || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Results Entry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Quickly assign band scores to candidates\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    successMessage\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-lg rounded-xl overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-8 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/4\",\n                                        children: \"Candidate Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8\",\n                                        children: \"\\uD83C\\uDFA7 Listening\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8\",\n                                        children: \"\\uD83D\\uDCD6 Reading\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8\",\n                                        children: \"✍️ Writing T1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8\",\n                                        children: \"✍️ Writing T2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8\",\n                                        children: \"\\uD83D\\uDDE3️ Speaking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: candidates.map((candidate)=>{\n                                const scores = bandScores[candidate.id] || {};\n                                const overallScore = calculateOverallBandScore(scores);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-blue-50/30 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-8 py-6 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-white\",\n                                                                children: candidate.candidateNumber\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-base font-semibold text-gray-900 truncate\",\n                                                                children: candidate.fullName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600 flex items-center space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCC5 \",\n                                                                        new Date(candidate.testDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            overallScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                    children: [\n                                                                        \"\\uD83C\\uDFAF Overall: \",\n                                                                        overallScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-6 whitespace-nowrap text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                value: scores.listening,\n                                                onChange: (score)=>updateBandScore(candidate.id, 'listening', score),\n                                                onSave: ()=>saveBandScore(candidate.id, 'listening'),\n                                                disabled: isSaving\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-6 whitespace-nowrap text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                value: scores.reading,\n                                                onChange: (score)=>updateBandScore(candidate.id, 'reading', score),\n                                                onSave: ()=>saveBandScore(candidate.id, 'reading'),\n                                                disabled: isSaving\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-6 whitespace-nowrap text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                value: scores.writingTask1,\n                                                onChange: (score)=>updateBandScore(candidate.id, 'writingTask1', score),\n                                                onSave: ()=>saveBandScore(candidate.id, 'writingTask1'),\n                                                disabled: isSaving\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-6 whitespace-nowrap text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                value: scores.writingTask2,\n                                                onChange: (score)=>updateBandScore(candidate.id, 'writingTask2', score),\n                                                onSave: ()=>saveBandScore(candidate.id, 'writingTask2'),\n                                                disabled: isSaving\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-6 whitespace-nowrap text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                value: scores.speaking,\n                                                onChange: (score)=>updateBandScore(candidate.id, 'speaking', score),\n                                                onSave: ()=>saveBandScore(candidate.id, 'speaking'),\n                                                disabled: isSaving\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-6 whitespace-nowrap text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    var _candidate_result;\n                                                    return router.push(\"/dashboard/results/\".concat(((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) || candidate.id));\n                                                },\n                                                className: \"inline-flex items-center justify-center w-8 h-8 text-blue-600 hover:text-white hover:bg-blue-600 rounded-full transition-colors duration-200\",\n                                                title: \"View Details\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, candidate.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(ResultsEntryPage, \"Cnpn/yrpDs3IaZaLQnTux4eSV2M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = ResultsEntryPage;\nfunction BandScoreSelector(param) {\n    let { value, onChange, onSave, disabled } = param;\n    _s1();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BandScoreSelector.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"BandScoreSelector.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"BandScoreSelector.useEffect.handleClickOutside\"];\n            if (isOpen) {\n                document.addEventListener('mousedown', handleClickOutside);\n            }\n            return ({\n                \"BandScoreSelector.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"BandScoreSelector.useEffect\"];\n        }\n    }[\"BandScoreSelector.useEffect\"], [\n        isOpen\n    ]);\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-500 border-gray-300 shadow-sm';\n        if (score >= 8.5) return 'bg-gradient-to-r from-green-400 to-emerald-500 text-white border-green-400 shadow-md';\n        if (score >= 7.0) return 'bg-gradient-to-r from-blue-400 to-blue-500 text-white border-blue-400 shadow-md';\n        if (score >= 6.0) return 'bg-gradient-to-r from-yellow-400 to-amber-500 text-white border-yellow-400 shadow-md';\n        if (score >= 5.0) return 'bg-gradient-to-r from-orange-400 to-orange-500 text-white border-orange-400 shadow-md';\n        return 'bg-gradient-to-r from-red-400 to-red-500 text-white border-red-400 shadow-md';\n    };\n    const getBandScoreIcon = (score)=>{\n        if (score === null) return '⏳';\n        if (score >= 8.5) return '🌟';\n        if (score >= 7.0) return '🎯';\n        if (score >= 6.0) return '✅';\n        if (score >= 5.0) return '📈';\n        return '📊';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"inline-flex items-center justify-center px-4 py-2.5 border-2 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-w-[100px] \".concat(getBandScoreColor(value), \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-lg'),\n                disabled: disabled,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-1\",\n                        children: getBandScoreIcon(value)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    value !== null ? \"\".concat(value) : 'Pending'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-20 mt-2 w-36 bg-white border border-gray-200 rounded-xl shadow-xl overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-2 max-h-64 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onChange(null);\n                                setIsOpen(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2.5 text-left text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: \"⏳\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this),\n                                \"Pending\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-100 my-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, this),\n                        BAND_SCORES.map((score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onChange(score);\n                                    setIsOpen(false);\n                                    setTimeout(onSave, 100); // Auto-save after selection\n                                },\n                                className: \"flex items-center w-full px-4 py-2.5 text-left text-sm text-gray-700 hover:bg-blue-50 transition-colors duration-150\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: getBandScoreIcon(score)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Band \",\n                                    score\n                                ]\n                            }, score, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 399,\n        columnNumber: 5\n    }, this);\n}\n_s1(BandScoreSelector, \"uhOyve9TWk+bvhPJTPlaMsUEQAY=\");\n_c1 = BandScoreSelector;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResultsEntryPage\");\n$RefreshReg$(_c1, \"BandScoreSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function disabledLog() {}\n    function disableLogs() {\n      if (0 === disabledDepth) {\n        prevLog = console.log;\n        prevInfo = console.info;\n        prevWarn = console.warn;\n        prevError = console.error;\n        prevGroup = console.group;\n        prevGroupCollapsed = console.groupCollapsed;\n        prevGroupEnd = console.groupEnd;\n        var props = {\n          configurable: !0,\n          enumerable: !0,\n          value: disabledLog,\n          writable: !0\n        };\n        Object.defineProperties(console, {\n          info: props,\n          log: props,\n          warn: props,\n          error: props,\n          group: props,\n          groupCollapsed: props,\n          groupEnd: props\n        });\n      }\n      disabledDepth++;\n    }\n    function reenableLogs() {\n      disabledDepth--;\n      if (0 === disabledDepth) {\n        var props = { configurable: !0, enumerable: !0, writable: !0 };\n        Object.defineProperties(console, {\n          log: assign({}, props, { value: prevLog }),\n          info: assign({}, props, { value: prevInfo }),\n          warn: assign({}, props, { value: prevWarn }),\n          error: assign({}, props, { value: prevError }),\n          group: assign({}, props, { value: prevGroup }),\n          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n          groupEnd: assign({}, props, { value: prevGroupEnd })\n        });\n      }\n      0 > disabledDepth &&\n        console.error(\n          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n        );\n    }\n    function describeBuiltInComponentFrame(name) {\n      if (void 0 === prefix)\n        try {\n          throw Error();\n        } catch (x) {\n          var match = x.stack.trim().match(/\\n( *(at )?)/);\n          prefix = (match && match[1]) || \"\";\n          suffix =\n            -1 < x.stack.indexOf(\"\\n    at\")\n              ? \" (<anonymous>)\"\n              : -1 < x.stack.indexOf(\"@\")\n                ? \"@unknown:0:0\"\n                : \"\";\n        }\n      return \"\\n\" + prefix + name + suffix;\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      if (!fn || reentry) return \"\";\n      var frame = componentFrameCache.get(fn);\n      if (void 0 !== frame) return frame;\n      reentry = !0;\n      frame = Error.prepareStackTrace;\n      Error.prepareStackTrace = void 0;\n      var previousDispatcher = null;\n      previousDispatcher = ReactSharedInternals.H;\n      ReactSharedInternals.H = null;\n      disableLogs();\n      try {\n        var RunInRootFrame = {\n          DetermineComponentFrameRoot: function () {\n            try {\n              if (construct) {\n                var Fake = function () {\n                  throw Error();\n                };\n                Object.defineProperty(Fake.prototype, \"props\", {\n                  set: function () {\n                    throw Error();\n                  }\n                });\n                if (\"object\" === typeof Reflect && Reflect.construct) {\n                  try {\n                    Reflect.construct(Fake, []);\n                  } catch (x) {\n                    var control = x;\n                  }\n                  Reflect.construct(fn, [], Fake);\n                } else {\n                  try {\n                    Fake.call();\n                  } catch (x$0) {\n                    control = x$0;\n                  }\n                  fn.call(Fake.prototype);\n                }\n              } else {\n                try {\n                  throw Error();\n                } catch (x$1) {\n                  control = x$1;\n                }\n                (Fake = fn()) &&\n                  \"function\" === typeof Fake.catch &&\n                  Fake.catch(function () {});\n              }\n            } catch (sample) {\n              if (sample && control && \"string\" === typeof sample.stack)\n                return [sample.stack, control.stack];\n            }\n            return [null, null];\n          }\n        };\n        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n          \"DetermineComponentFrameRoot\";\n        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n          RunInRootFrame.DetermineComponentFrameRoot,\n          \"name\"\n        );\n        namePropDescriptor &&\n          namePropDescriptor.configurable &&\n          Object.defineProperty(\n            RunInRootFrame.DetermineComponentFrameRoot,\n            \"name\",\n            { value: \"DetermineComponentFrameRoot\" }\n          );\n        var _RunInRootFrame$Deter =\n            RunInRootFrame.DetermineComponentFrameRoot(),\n          sampleStack = _RunInRootFrame$Deter[0],\n          controlStack = _RunInRootFrame$Deter[1];\n        if (sampleStack && controlStack) {\n          var sampleLines = sampleStack.split(\"\\n\"),\n            controlLines = controlStack.split(\"\\n\");\n          for (\n            _RunInRootFrame$Deter = namePropDescriptor = 0;\n            namePropDescriptor < sampleLines.length &&\n            !sampleLines[namePropDescriptor].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            namePropDescriptor++;\n          for (\n            ;\n            _RunInRootFrame$Deter < controlLines.length &&\n            !controlLines[_RunInRootFrame$Deter].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            _RunInRootFrame$Deter++;\n          if (\n            namePropDescriptor === sampleLines.length ||\n            _RunInRootFrame$Deter === controlLines.length\n          )\n            for (\n              namePropDescriptor = sampleLines.length - 1,\n                _RunInRootFrame$Deter = controlLines.length - 1;\n              1 <= namePropDescriptor &&\n              0 <= _RunInRootFrame$Deter &&\n              sampleLines[namePropDescriptor] !==\n                controlLines[_RunInRootFrame$Deter];\n\n            )\n              _RunInRootFrame$Deter--;\n          for (\n            ;\n            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n            namePropDescriptor--, _RunInRootFrame$Deter--\n          )\n            if (\n              sampleLines[namePropDescriptor] !==\n              controlLines[_RunInRootFrame$Deter]\n            ) {\n              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n                do\n                  if (\n                    (namePropDescriptor--,\n                    _RunInRootFrame$Deter--,\n                    0 > _RunInRootFrame$Deter ||\n                      sampleLines[namePropDescriptor] !==\n                        controlLines[_RunInRootFrame$Deter])\n                  ) {\n                    var _frame =\n                      \"\\n\" +\n                      sampleLines[namePropDescriptor].replace(\n                        \" at new \",\n                        \" at \"\n                      );\n                    fn.displayName &&\n                      _frame.includes(\"<anonymous>\") &&\n                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n                    \"function\" === typeof fn &&\n                      componentFrameCache.set(fn, _frame);\n                    return _frame;\n                  }\n                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n              }\n              break;\n            }\n        }\n      } finally {\n        (reentry = !1),\n          (ReactSharedInternals.H = previousDispatcher),\n          reenableLogs(),\n          (Error.prepareStackTrace = frame);\n      }\n      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n        ? describeBuiltInComponentFrame(sampleLines)\n        : \"\";\n      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n      return sampleLines;\n    }\n    function describeUnknownElementTypeFrameInDEV(type) {\n      if (null == type) return \"\";\n      if (\"function\" === typeof type) {\n        var prototype = type.prototype;\n        return describeNativeComponentFrame(\n          type,\n          !(!prototype || !prototype.isReactComponent)\n        );\n      }\n      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame(\"Suspense\");\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame(\"SuspenseList\");\n      }\n      if (\"object\" === typeof type)\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return (type = describeNativeComponentFrame(type.render, !1)), type;\n          case REACT_MEMO_TYPE:\n            return describeUnknownElementTypeFrameInDEV(type.type);\n          case REACT_LAZY_TYPE:\n            prototype = type._payload;\n            type = type._init;\n            try {\n              return describeUnknownElementTypeFrameInDEV(type(prototype));\n            } catch (x) {}\n        }\n      return \"\";\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, self, source, owner, props) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      if (\n        \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        type === REACT_OFFSCREEN_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n            void 0 !== type.getModuleId))\n      ) {\n        var children = config.children;\n        if (void 0 !== children)\n          if (isStaticChildren)\n            if (isArrayImpl(children)) {\n              for (\n                isStaticChildren = 0;\n                isStaticChildren < children.length;\n                isStaticChildren++\n              )\n                validateChildKeys(children[isStaticChildren], type);\n              Object.freeze && Object.freeze(children);\n            } else\n              console.error(\n                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n              );\n          else validateChildKeys(children, type);\n      } else {\n        children = \"\";\n        if (\n          void 0 === type ||\n          (\"object\" === typeof type &&\n            null !== type &&\n            0 === Object.keys(type).length)\n        )\n          children +=\n            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n        null === type\n          ? (isStaticChildren = \"null\")\n          : isArrayImpl(type)\n            ? (isStaticChildren = \"array\")\n            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n              ? ((isStaticChildren =\n                  \"<\" +\n                  (getComponentNameFromType(type.type) || \"Unknown\") +\n                  \" />\"),\n                (children =\n                  \" Did you accidentally export a JSX literal instead of a component?\"))\n              : (isStaticChildren = typeof type);\n        console.error(\n          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n          isStaticChildren,\n          children\n        );\n      }\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n    }\n    function validateChildKeys(node, parentType) {\n      if (\n        \"object\" === typeof node &&\n        node &&\n        node.$$typeof !== REACT_CLIENT_REFERENCE\n      )\n        if (isArrayImpl(node))\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            isValidElement(child) && validateExplicitKey(child, parentType);\n          }\n        else if (isValidElement(node))\n          node._store && (node._store.validated = 1);\n        else if (\n          (null === node || \"object\" !== typeof node\n            ? (i = null)\n            : ((i =\n                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n                node[\"@@iterator\"]),\n              (i = \"function\" === typeof i ? i : null)),\n          \"function\" === typeof i &&\n            i !== node.entries &&\n            ((i = i.call(node)), i !== node))\n        )\n          for (; !(node = i.next()).done; )\n            isValidElement(node.value) &&\n              validateExplicitKey(node.value, parentType);\n    }\n    function isValidElement(object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    }\n    function validateExplicitKey(element, parentType) {\n      if (\n        element._store &&\n        !element._store.validated &&\n        null == element.key &&\n        ((element._store.validated = 1),\n        (parentType = getCurrentComponentErrorInfo(parentType)),\n        !ownerHasKeyUseWarning[parentType])\n      ) {\n        ownerHasKeyUseWarning[parentType] = !0;\n        var childOwner = \"\";\n        element &&\n          null != element._owner &&\n          element._owner !== getOwner() &&\n          ((childOwner = null),\n          \"number\" === typeof element._owner.tag\n            ? (childOwner = getComponentNameFromType(element._owner.type))\n            : \"string\" === typeof element._owner.name &&\n              (childOwner = element._owner.name),\n          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n        ReactSharedInternals.getCurrentStack = function () {\n          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n          return stack;\n        };\n        console.error(\n          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n          parentType,\n          childOwner\n        );\n        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n      }\n    }\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = \"\",\n        owner = getOwner();\n      owner &&\n        (owner = getComponentNameFromType(owner.type)) &&\n        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n      info ||\n        ((parentType = getComponentNameFromType(parentType)) &&\n          (info =\n            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n      return info;\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      assign = Object.assign,\n      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n      isArrayImpl = Array.isArray,\n      disabledDepth = 0,\n      prevLog,\n      prevInfo,\n      prevWarn,\n      prevError,\n      prevGroup,\n      prevGroupCollapsed,\n      prevGroupEnd;\n    disabledLog.__reactDisabledLog = !0;\n    var prefix,\n      suffix,\n      reentry = !1;\n    var componentFrameCache = new (\n      \"function\" === typeof WeakMap ? WeakMap : Map\n    )();\n    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {},\n      ownerHasKeyUseWarning = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@auth/core/errors.js":
/*!*******************************************!*\
  !*** ./node_modules/@auth/core/errors.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDenied: () => (/* binding */ AccessDenied),\n/* harmony export */   AccountNotLinked: () => (/* binding */ AccountNotLinked),\n/* harmony export */   AdapterError: () => (/* binding */ AdapterError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   CallbackRouteError: () => (/* binding */ CallbackRouteError),\n/* harmony export */   CredentialsSignin: () => (/* binding */ CredentialsSignin),\n/* harmony export */   DuplicateConditionalUI: () => (/* binding */ DuplicateConditionalUI),\n/* harmony export */   EmailSignInError: () => (/* binding */ EmailSignInError),\n/* harmony export */   ErrorPageLoop: () => (/* binding */ ErrorPageLoop),\n/* harmony export */   EventError: () => (/* binding */ EventError),\n/* harmony export */   ExperimentalFeatureNotEnabled: () => (/* binding */ ExperimentalFeatureNotEnabled),\n/* harmony export */   InvalidCallbackUrl: () => (/* binding */ InvalidCallbackUrl),\n/* harmony export */   InvalidCheck: () => (/* binding */ InvalidCheck),\n/* harmony export */   InvalidEndpoints: () => (/* binding */ InvalidEndpoints),\n/* harmony export */   InvalidProvider: () => (/* binding */ InvalidProvider),\n/* harmony export */   JWTSessionError: () => (/* binding */ JWTSessionError),\n/* harmony export */   MissingAdapter: () => (/* binding */ MissingAdapter),\n/* harmony export */   MissingAdapterMethods: () => (/* binding */ MissingAdapterMethods),\n/* harmony export */   MissingAuthorize: () => (/* binding */ MissingAuthorize),\n/* harmony export */   MissingCSRF: () => (/* binding */ MissingCSRF),\n/* harmony export */   MissingSecret: () => (/* binding */ MissingSecret),\n/* harmony export */   MissingWebAuthnAutocomplete: () => (/* binding */ MissingWebAuthnAutocomplete),\n/* harmony export */   OAuthAccountNotLinked: () => (/* binding */ OAuthAccountNotLinked),\n/* harmony export */   OAuthCallbackError: () => (/* binding */ OAuthCallbackError),\n/* harmony export */   OAuthProfileParseError: () => (/* binding */ OAuthProfileParseError),\n/* harmony export */   OAuthSignInError: () => (/* binding */ OAuthSignInError),\n/* harmony export */   SessionTokenError: () => (/* binding */ SessionTokenError),\n/* harmony export */   SignInError: () => (/* binding */ SignInError),\n/* harmony export */   SignOutError: () => (/* binding */ SignOutError),\n/* harmony export */   UnknownAction: () => (/* binding */ UnknownAction),\n/* harmony export */   UnsupportedStrategy: () => (/* binding */ UnsupportedStrategy),\n/* harmony export */   UntrustedHost: () => (/* binding */ UntrustedHost),\n/* harmony export */   Verification: () => (/* binding */ Verification),\n/* harmony export */   WebAuthnVerificationError: () => (/* binding */ WebAuthnVerificationError),\n/* harmony export */   isClientError: () => (/* binding */ isClientError)\n/* harmony export */ });\n/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nclass AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nclass SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nclass AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nclass AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nclass CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nclass ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nclass EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nclass InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nclass CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nclass InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nclass InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nclass JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nclass MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nclass MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nclass MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nclass MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nclass OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nclass OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nclass OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nclass SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nclass OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nclass EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nclass SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nclass UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nclass UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nclass InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nclass UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nclass Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nclass MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nfunction isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nclass DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nclass MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nclass WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nclass AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nclass ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@auth/core/errors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(app-pages-browser)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ var _s = $RefreshSig$();\n\n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger) {\n    let req = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n    const url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n    try {\n        var _req_headers;\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(req === null || req === void 0 ? void 0 : (_req_headers = req.headers) === null || _req_headers === void 0 ? void 0 : _req_headers.cookie) ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req === null || req === void 0 ? void 0 : req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (typeof window === \"undefined\") {\n        // Return absolute path when called server side\n        return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    _s();\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n_s(useOnline, \"9TTTpdMr0LAEvNmxVj+grReKWwQ=\");\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = \"https://\".concat(url);\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = \"\".concat(_url.origin).concat(path);\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-auth/lib/client.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("var react__WEBPACK_IMPORTED_MODULE_1___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(app-pages-browser)/./node_modules/next-auth/lib/client.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ var _React_createContext;\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nvar _process_env_NEXTAUTH_URL, _process_env_NEXTAUTH_URL_INTERNAL, _ref, _process_env_NEXTAUTH_URL_INTERNAL1;\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_process_env_NEXTAUTH_URL = process.env.NEXTAUTH_URL) !== null && _process_env_NEXTAUTH_URL !== void 0 ? _process_env_NEXTAUTH_URL : process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_ref = (_process_env_NEXTAUTH_URL_INTERNAL = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process_env_NEXTAUTH_URL_INTERNAL !== void 0 ? _process_env_NEXTAUTH_URL_INTERNAL : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_process_env_NEXTAUTH_URL_INTERNAL1 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process_env_NEXTAUTH_URL_INTERNAL1 !== void 0 ? _process_env_NEXTAUTH_URL_INTERNAL1 : process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = (_React_createContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext) === null || _React_createContext === void 0 ? void 0 : _React_createContext.call(/*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_1__, 2))), undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    _s();\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options !== null && options !== void 0 ? options : {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = \"\".concat(__NEXTAUTH.basePath, \"/signin?\").concat(new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                }));\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\n_s(useSession, \"fo5J65/TYWua//m8QE+oTrUC6Kk=\");\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    var _params_broadcast;\n    if ((_params_broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params_broadcast !== void 0 ? _params_broadcast : true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    var _response_csrfToken;\n    return (_response_csrfToken = response === null || response === void 0 ? void 0 : response.csrfToken) !== null && _response_csrfToken !== void 0 ? _response_csrfToken : \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options !== null && options !== void 0 ? options : {};\n    const { redirect = true, redirectTo = callbackUrl !== null && callbackUrl !== void 0 ? callbackUrl : window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = \"\".concat(baseUrl, \"/error\");\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: redirectTo\n        }));\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            'Provider id \"'.concat(provider, '\" refers to a WebAuthn provider.'),\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = \"\".concat(baseUrl, \"/\").concat(providerType === \"credentials\" ? \"callback\" : \"signin\", \"/\").concat(provider);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(\"\".concat(signInUrl, \"?\").concat(new URLSearchParams(authorizationParams)), {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        var _data_url;\n        const url = (_data_url = data.url) !== null && _data_url !== void 0 ? _data_url : redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    var _searchParams_get;\n    const error = (_searchParams_get = new URL(data.url).searchParams.get(\"error\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : undefined;\n    var _searchParams_get1;\n    const code = (_searchParams_get1 = new URL(data.url).searchParams.get(\"code\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    var _options_callbackUrl;\n    const { redirect = true, redirectTo = (_options_callbackUrl = options === null || options === void 0 ? void 0 : options.callbackUrl) !== null && _options_callbackUrl !== void 0 ? _options_callbackUrl : window.location.href } = options !== null && options !== void 0 ? options : {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(\"\".concat(baseUrl, \"/signout\"), {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        var _data_url;\n        const url = (_data_url = data.url) !== null && _data_url !== void 0 ? _data_url : redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    _s1();\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async function() {\n                    let { event } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n_s1(SessionProvider, \"2gNWkA1ll3+1JambdW4uXB3blzw=\", false, function() {\n    return [\n        _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline\n    ];\n});\n_c = SessionProvider;\nvar _c;\n$RefreshReg$(_c, \"SessionProvider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-auth/react.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5CIELTS-Certification-System%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cresults%5C%5Centry%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);