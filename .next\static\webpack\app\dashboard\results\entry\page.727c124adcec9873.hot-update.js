"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/results/entry/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/results/entry/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuickEntryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Band score options (0.0 to 9.0 in 0.5 increments)\nconst BAND_SCORE_OPTIONS = [\n    0.0,\n    0.5,\n    1.0,\n    1.5,\n    2.0,\n    2.5,\n    3.0,\n    3.5,\n    4.0,\n    4.5,\n    5.0,\n    5.5,\n    6.0,\n    6.5,\n    7.0,\n    7.5,\n    8.0,\n    8.5,\n    9.0\n];\nfunction QuickEntryPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [candidates, setCandidates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bandScores, setBandScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [saveStatus, setSaveStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch candidates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickEntryPage.useEffect\": ()=>{\n            fetchCandidates();\n        }\n    }[\"QuickEntryPage.useEffect\"], []);\n    const fetchCandidates = async ()=>{\n        try {\n            var _data_candidates;\n            setLoading(true);\n            const response = await fetch('/api/checker/candidates?includeResults=true');\n            if (!response.ok) {\n                throw new Error('Failed to fetch candidates');\n            }\n            const data = await response.json();\n            setCandidates(data.candidates || []);\n            // Initialize band scores from existing results\n            const initialScores = {};\n            const initialSaveStatus = {};\n            (_data_candidates = data.candidates) === null || _data_candidates === void 0 ? void 0 : _data_candidates.forEach((candidate)=>{\n                var _candidate_result, _candidate_result1, _candidate_result2, _candidate_result3, _candidate_result4;\n                initialScores[candidate.id] = {\n                    listening: ((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.listeningBandScore) ? parseFloat(candidate.result.listeningBandScore.toString()) : null,\n                    reading: ((_candidate_result1 = candidate.result) === null || _candidate_result1 === void 0 ? void 0 : _candidate_result1.readingBandScore) ? parseFloat(candidate.result.readingBandScore.toString()) : null,\n                    writingTask1: ((_candidate_result2 = candidate.result) === null || _candidate_result2 === void 0 ? void 0 : _candidate_result2.writingTask1Score) ? parseFloat(candidate.result.writingTask1Score.toString()) : null,\n                    writingTask2: ((_candidate_result3 = candidate.result) === null || _candidate_result3 === void 0 ? void 0 : _candidate_result3.writingTask2Score) ? parseFloat(candidate.result.writingTask2Score.toString()) : null,\n                    speaking: ((_candidate_result4 = candidate.result) === null || _candidate_result4 === void 0 ? void 0 : _candidate_result4.speakingBandScore) ? parseFloat(candidate.result.speakingBandScore.toString()) : null\n                };\n                initialSaveStatus[candidate.id] = {\n                    listening: 'idle',\n                    reading: 'idle',\n                    writingTask1: 'idle',\n                    writingTask2: 'idle',\n                    speaking: 'idle'\n                };\n            });\n            setBandScores(initialScores);\n            setSaveStatus(initialSaveStatus);\n        } catch (error) {\n            console.error('Error fetching candidates:', error);\n            setError('Failed to load candidates. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Calculate overall band score\n    const calculateOverallBandScore = (scores)=>{\n        const { listening, reading, writingTask1, writingTask2, speaking } = scores;\n        // Calculate writing band score from tasks\n        let writingBand = null;\n        if (writingTask1 !== null && writingTask2 !== null) {\n            writingBand = (writingTask1 + writingTask2) / 2;\n        }\n        const validScores = [\n            listening,\n            reading,\n            writingBand,\n            speaking\n        ].filter((score)=>score !== null);\n        if (validScores.length === 4) {\n            const average = validScores.reduce((sum, score)=>sum + score, 0) / 4;\n            return Math.round(average * 2) / 2; // Round to nearest 0.5\n        }\n        return null;\n    };\n    // Update band score for a specific candidate and skill\n    const updateBandScore = (candidateId, skill, value)=>{\n        setBandScores((prev)=>({\n                ...prev,\n                [candidateId]: {\n                    ...prev[candidateId],\n                    [skill]: value\n                }\n            }));\n        // Auto-save after a short delay\n        setTimeout(()=>{\n            saveBandScore(candidateId, skill, value);\n        }, 500);\n    };\n    // Save band score to database\n    const saveBandScore = async (candidateId, skill, value)=>{\n        const candidate = candidates.find((c)=>c.id === candidateId);\n        if (!candidate) return;\n        // Update save status to saving\n        setSaveStatus((prev)=>({\n                ...prev,\n                [candidateId]: {\n                    ...prev[candidateId],\n                    [skill]: 'saving'\n                }\n            }));\n        try {\n            var _candidate_result;\n            const scores = bandScores[candidateId];\n            if (!scores) return;\n            const overallBandScore = calculateOverallBandScore(scores);\n            // Prepare the data for API\n            const resultData = {\n                candidateId,\n                listeningBandScore: scores.listening,\n                readingBandScore: scores.reading,\n                writingTask1Score: scores.writingTask1,\n                writingTask2Score: scores.writingTask2,\n                writingBandScore: scores.writingTask1 && scores.writingTask2 ? (scores.writingTask1 + scores.writingTask2) / 2 : null,\n                speakingBandScore: scores.speaking,\n                overallBandScore,\n                status: 'completed'\n            };\n            const url = candidate.hasResult ? \"/api/checker/results/\".concat((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) : '/api/checker/results';\n            const method = candidate.hasResult ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(resultData)\n            });\n            if (response.ok) {\n                // Update save status to saved\n                setSaveStatus((prev)=>({\n                        ...prev,\n                        [candidateId]: {\n                            ...prev[candidateId],\n                            [skill]: 'saved'\n                        }\n                    }));\n                // Show success message\n                const skillName = skill === 'writingTask1' ? 'Writing Task 1' : skill === 'writingTask2' ? 'Writing Task 2' : skill.charAt(0).toUpperCase() + skill.slice(1);\n                setSuccessMessage(\"\".concat(skillName, \" score saved for \").concat(candidate.candidateNumber));\n                setTimeout(()=>setSuccessMessage(null), 3000);\n                // Refresh candidates to get updated data\n                fetchCandidates();\n            } else {\n                throw new Error('Failed to save score');\n            }\n        } catch (error) {\n            console.error('Error saving score:', error);\n            // Update save status to error\n            setSaveStatus((prev)=>({\n                    ...prev,\n                    [candidateId]: {\n                        ...prev[candidateId],\n                        [skill]: 'error'\n                    }\n                }));\n            setError('Failed to save score. Please try again.');\n            setTimeout(()=>setError(null), 5000);\n        }\n    };\n    // Band Score Selector Component\n    const BandScoreSelector = (param)=>{\n        let { candidateId, skill, value, disabled = false } = param;\n        var _saveStatus_candidateId;\n        const status = ((_saveStatus_candidateId = saveStatus[candidateId]) === null || _saveStatus_candidateId === void 0 ? void 0 : _saveStatus_candidateId[skill]) || 'idle';\n        const getStatusColor = ()=>{\n            switch(status){\n                case 'saving':\n                    return 'border-yellow-300 bg-yellow-50';\n                case 'saved':\n                    return 'border-green-300 bg-green-50';\n                case 'error':\n                    return 'border-red-300 bg-red-50';\n                default:\n                    return 'border-gray-300 bg-white';\n            }\n        };\n        const getStatusIcon = ()=>{\n            switch(status){\n                case 'saving':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-3 w-3 animate-spin text-yellow-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 31\n                    }, this);\n                case 'saved':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-3 w-3 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 30\n                    }, this);\n                case 'error':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-3 w-3 text-red-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 30\n                    }, this);\n                default:\n                    return null;\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    value: value || '',\n                    onChange: (e)=>{\n                        const newValue = e.target.value ? parseFloat(e.target.value) : null;\n                        updateBandScore(candidateId, skill, newValue);\n                    },\n                    disabled: disabled,\n                    className: \"w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(getStatusColor(), \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: \"\",\n                            children: \"Select\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        BAND_SCORE_OPTIONS.map((score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: score,\n                                children: score.toFixed(1)\n                            }, score, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                getStatusIcon() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                    children: getStatusIcon()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg text-gray-600\",\n                        children: \"Loading candidates...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Quick Entry\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Fast band assignment for test checkers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchCandidates,\n                        className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            \"Refresh\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                    successMessage\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-blue-800 font-medium\",\n                    children: [\n                        \"Total Candidates: \",\n                        candidates.length\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-lg rounded-xl overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center text-gray-500\",\n                    children: \"Table implementation will be added in the next chunk...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 317,\n        columnNumber: 5\n    }, this);\n}\n_s(QuickEntryPage, \"PJWM77INou2supbZyDoJ2YY154U=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = QuickEntryPage;\nvar _c;\n$RefreshReg$(_c, \"QuickEntryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx\n"));

/***/ })

});