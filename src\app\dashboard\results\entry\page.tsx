'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Check, Eye, Save, AlertCircle } from 'lucide-react';
import { useSession } from 'next-auth/react';

interface Candidate {
  id: string;
  candidateNumber: string;
  fullName: string;
  email: string;
  testDate: string;
  hasResult: boolean;
  result?: {
    id: string;
    listeningBandScore: number | null;
    readingBandScore: number | null;
    writingBandScore: number | null;
    speakingBandScore: number | null;
    overallBandScore: number | null;
    status: 'pending' | 'completed' | 'verified';
  };
}

interface BandScores {
  [candidateId: string]: {
    listening: number | null;
    reading: number | null;
    writingTask1: number | null;
    writingTask2: number | null;
    speaking: number | null;
  };
}

const BAND_SCORES = [1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0, 7.5, 8.0, 8.5, 9.0];

export default function ResultsEntryPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [bandScores, setBandScores] = useState<BandScores>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    fetchCandidates();
  }, [session, status, router]);

  const fetchCandidates = async () => {
    try {
      const response = await fetch('/api/checker/candidates?includeResults=true');
      if (response.ok) {
        const data = await response.json();
        setCandidates(data.candidates || []);

        // Initialize band scores from existing results
        const initialScores: BandScores = {};
        data.candidates?.forEach((candidate: Candidate) => {
          if (candidate.result) {
            initialScores[candidate.id] = {
              listening: candidate.result.listeningBandScore,
              reading: candidate.result.readingBandScore,
              writingTask1: null, // We'll need to calculate from writing band score
              writingTask2: null,
              speaking: candidate.result.speakingBandScore,
            };
          }
        });
        setBandScores(initialScores);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch candidates');
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      setError('An error occurred while fetching candidates');
    } finally {
      setIsLoading(false);
    }
  };

  const updateBandScore = (candidateId: string, skill: keyof BandScores[string], score: number | null) => {
    setBandScores(prev => ({
      ...prev,
      [candidateId]: {
        ...prev[candidateId], // Keep existing scores
        [skill]: score, // Update only the specific skill
      },
    }));
  };

  const calculateOverallBandScore = (scores: BandScores[string]) => {
    const { listening, reading, writingTask1, writingTask2, speaking } = scores;

    // Calculate writing band score from tasks
    let writingBand = null;
    if (writingTask1 !== null && writingTask2 !== null) {
      writingBand = (writingTask1 + writingTask2) / 2;
    }

    const validScores = [listening, reading, writingBand, speaking].filter(score => score !== null) as number[];

    if (validScores.length === 4) {
      const average = validScores.reduce((sum, score) => sum + score, 0) / 4;
      return Math.round(average * 2) / 2; // Round to nearest 0.5
    }

    return null;
  };

  const saveBandScore = async (candidateId: string, skill: keyof BandScores[string]) => {
    const scores = bandScores[candidateId];
    if (!scores) return;

    setIsSaving(true);
    try {
      const candidate = candidates.find(c => c.id === candidateId);
      if (!candidate) return;

      const overallBandScore = calculateOverallBandScore(scores);

      // Prepare the data for API
      const resultData = {
        candidateId,
        listeningBandScore: scores.listening,
        readingBandScore: scores.reading,
        writingTask1Score: scores.writingTask1,
        writingTask2Score: scores.writingTask2,
        writingBandScore: scores.writingTask1 && scores.writingTask2
          ? (scores.writingTask1 + scores.writingTask2) / 2
          : null,
        speakingBandScore: scores.speaking,
        overallBandScore,
        status: 'completed' as const,
      };

      const url = candidate.hasResult
        ? `/api/checker/results/${candidate.result?.id}`
        : '/api/checker/results';

      const method = candidate.hasResult ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resultData),
      });

      if (response.ok) {
        setSuccessMessage(`${skill} score saved for ${candidate.candidateNumber}`);
        setTimeout(() => setSuccessMessage(''), 3000);
        await fetchCandidates(); // Refresh data
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to save score');
      }
    } catch (error) {
      console.error('Error saving score:', error);
      setError('An error occurred while saving the score');
    } finally {
      setIsSaving(false);
    }
  };

  const getBandScoreColor = (score: number | null) => {
    if (score === null) return 'bg-gray-100 text-gray-400';
    if (score >= 8.5) return 'bg-green-100 text-green-800';
    if (score >= 7.0) return 'bg-blue-100 text-blue-800';
    if (score >= 6.0) return 'bg-yellow-100 text-yellow-800';
    if (score >= 5.0) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Results Entry</h1>
          <p className="text-gray-600">Quickly assign band scores to candidates</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center">
          <Check className="h-4 w-4 mr-2" />
          {successMessage}
        </div>
      )}

      {/* Results Entry Table */}
      <div className="bg-white shadow-lg rounded-xl overflow-hidden">
        <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
              <tr>
                <th className="px-8 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/4">
                  Candidate Information
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8">
                  🎧 Listening
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8">
                  📖 Reading
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8">
                  ✍️ Writing T1
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8">
                  ✍️ Writing T2
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8">
                  🗣️ Speaking
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider w-1/8">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {candidates.map((candidate) => {
                const scores = bandScores[candidate.id] || {};
                const overallScore = calculateOverallBandScore(scores);

                return (
                  <tr key={candidate.id} className="hover:bg-blue-50/30 transition-colors duration-200">
                    <td className="px-8 py-6 whitespace-nowrap">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-md">
                            <span className="text-sm font-bold text-white">
                              {candidate.candidateNumber}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-base font-semibold text-gray-900 truncate">
                            {candidate.fullName}
                          </div>
                          <div className="text-sm text-gray-600 flex items-center space-x-2">
                            <span>📅 {new Date(candidate.testDate).toLocaleDateString()}</span>
                          </div>
                          {overallScore && (
                            <div className="mt-1">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                🎯 Overall: {overallScore}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* Listening */}
                    <td className="px-4 py-6 whitespace-nowrap text-center">
                      <BandScoreSelector
                        value={scores.listening}
                        onChange={(score) => updateBandScore(candidate.id, 'listening', score)}
                        onSave={() => saveBandScore(candidate.id, 'listening')}
                        disabled={isSaving}
                      />
                    </td>

                    {/* Reading */}
                    <td className="px-4 py-6 whitespace-nowrap text-center">
                      <BandScoreSelector
                        value={scores.reading}
                        onChange={(score) => updateBandScore(candidate.id, 'reading', score)}
                        onSave={() => saveBandScore(candidate.id, 'reading')}
                        disabled={isSaving}
                      />
                    </td>

                    {/* Writing Task 1 */}
                    <td className="px-4 py-6 whitespace-nowrap text-center">
                      <BandScoreSelector
                        value={scores.writingTask1}
                        onChange={(score) => updateBandScore(candidate.id, 'writingTask1', score)}
                        onSave={() => saveBandScore(candidate.id, 'writingTask1')}
                        disabled={isSaving}
                      />
                    </td>

                    {/* Writing Task 2 */}
                    <td className="px-4 py-6 whitespace-nowrap text-center">
                      <BandScoreSelector
                        value={scores.writingTask2}
                        onChange={(score) => updateBandScore(candidate.id, 'writingTask2', score)}
                        onSave={() => saveBandScore(candidate.id, 'writingTask2')}
                        disabled={isSaving}
                      />
                    </td>

                    {/* Speaking */}
                    <td className="px-4 py-6 whitespace-nowrap text-center">
                      <BandScoreSelector
                        value={scores.speaking}
                        onChange={(score) => updateBandScore(candidate.id, 'speaking', score)}
                        onSave={() => saveBandScore(candidate.id, 'speaking')}
                        disabled={isSaving}
                      />
                    </td>

                    {/* Actions */}
                    <td className="px-4 py-6 whitespace-nowrap text-center">
                      <button
                        onClick={() => router.push(`/dashboard/results/${candidate.result?.id || candidate.id}`)}
                        className="inline-flex items-center justify-center w-8 h-8 text-blue-600 hover:text-white hover:bg-blue-600 rounded-full transition-colors duration-200"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
      </div>
    </div>
  );
}

interface BandScoreSelectorProps {
  value: number | null;
  onChange: (score: number | null) => void;
  onSave: () => void;
  disabled: boolean;
}

function BandScoreSelector({ value, onChange, onSave, disabled }: BandScoreSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const getBandScoreColor = (score: number | null) => {
    if (score === null) return 'bg-gray-100 text-gray-500 border-gray-300 shadow-sm';
    if (score >= 8.5) return 'bg-gradient-to-r from-green-400 to-emerald-500 text-white border-green-400 shadow-md';
    if (score >= 7.0) return 'bg-gradient-to-r from-blue-400 to-blue-500 text-white border-blue-400 shadow-md';
    if (score >= 6.0) return 'bg-gradient-to-r from-yellow-400 to-amber-500 text-white border-yellow-400 shadow-md';
    if (score >= 5.0) return 'bg-gradient-to-r from-orange-400 to-orange-500 text-white border-orange-400 shadow-md';
    return 'bg-gradient-to-r from-red-400 to-red-500 text-white border-red-400 shadow-md';
  };

  const getBandScoreIcon = (score: number | null) => {
    if (score === null) return '⏳';
    if (score >= 8.5) return '🌟';
    if (score >= 7.0) return '🎯';
    if (score >= 6.0) return '✅';
    if (score >= 5.0) return '📈';
    return '📊';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex items-center justify-center px-4 py-2.5 border-2 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-w-[100px] ${getBandScoreColor(value)} ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-lg'}`}
        disabled={disabled}
      >
        <span className="mr-1">{getBandScoreIcon(value)}</span>
        {value !== null ? `${value}` : 'Pending'}
      </button>

      {isOpen && (
        <div className="absolute z-20 mt-2 w-36 bg-white border border-gray-200 rounded-xl shadow-xl overflow-hidden">
          <div className="py-2 max-h-64 overflow-y-auto">
            <button
              onClick={() => {
                onChange(null);
                setIsOpen(false);
              }}
              className="flex items-center w-full px-4 py-2.5 text-left text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
            >
              <span className="mr-2">⏳</span>
              Pending
            </button>
            <div className="border-t border-gray-100 my-1"></div>
            {BAND_SCORES.map((score) => (
              <button
                key={score}
                onClick={() => {
                  onChange(score);
                  setIsOpen(false);
                  setTimeout(onSave, 100); // Auto-save after selection
                }}
                className="flex items-center w-full px-4 py-2.5 text-left text-sm text-gray-700 hover:bg-blue-50 transition-colors duration-150"
              >
                <span className="mr-2">{getBandScoreIcon(score)}</span>
                Band {score}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
